# Solution Pattern Configuration

## Overview

The solution pattern filtering system has been refactored to use configuration-based pattern selection instead of intelligent pattern detection. Each SAP system client now has its solution pattern explicitly configured in the `config/system_client.yaml` file.

## Configuration

### System Client Configuration

In `config/system_client.yaml`, each system now includes a `solution_pattern` field:

```yaml
"R8K.079":
  mshost: ldcsr8k.devsys.net.sap
  group: PUBLIC
  r3name: R8K
  client: "079"
  user: ANZEIGER
  passwd: display
  # Solution pattern configuration
  solution_pattern: "cloud_enterprise"

"OC4.077":
  ashost: ldcioc4.wdf.sap.corp
  sysnr: "00"
  client: "077"
  user: ANZEIGER
  passwd: display
  solution_pattern: "onprem_hana"
```

### Available Patterns

- **`cloud_enterprise`**: Matches pattern `^BP_CLD_ENTPR_S4CLD\d{4}_[A-Z0-9]{2}V\d{2}$`
  - Example: `BP_CLD_ENTPR_S4CLD2024_DEV01`
  
- **`onprem_hana`**: Matches pattern `^BP_S4BL_S4HANAX_[A-Z0-9]{2}V1$`
  - Example: `BP_S4BL_S4HANAX_DEV1`

## Code Changes

### New Function: `get_solution_pattern()`

Added to `common/config.py`:

```python
def get_solution_pattern(sys_client: str) -> str:
    """Get solution pattern for a specific system client."""
```

### Modified Function: `filter_solution()`

Updated in `solbuld.py` to accept `sys_client` parameter:

```python
def filter_solution(df: pd.DataFrame, column_name: str, sys_client: str) -> pd.DataFrame:
    """Filter DataFrame based on configured solution pattern."""
```

## Benefits

1. **Explicit Configuration**: No more guessing which pattern to use
2. **System-Specific**: Each system can have its own pattern configuration
3. **Maintainable**: Easy to add new systems or change patterns
4. **Predictable**: Behavior is deterministic based on configuration
5. **Error Handling**: Clear error messages for missing or invalid configurations

## Migration

All existing calls to `filter_solution()` have been updated to pass the `sys_client` parameter. The function now reads the pattern configuration instead of performing intelligent detection.

## Error Handling

The system will raise `ValueError` exceptions for:
- System configuration not found
- Missing solution pattern configuration
- Invalid pattern type (not 'cloud_enterprise' or 'onprem_hana')
