import os
import pandas as pd
from functools import wraps
from datetime import datetime
import inspect
import hashlib
from common.logger import logger
from common.paths import paths
from common.config import get_cache_expiration_seconds


def _generate_cache_filename(sys_client, func_name, args, kwargs):
    """Generate a cache filename that includes function parameters.

    Args:
        sys_client: The system client identifier
        func_name: The function name
        args: Positional arguments (excluding self/instance)
        kwargs: Keyword arguments

    Returns:
        str: A filename-safe cache filename
    """
    # Start with base filename
    filename_parts = [sys_client, func_name]

    # Add positional arguments (skip first argument which is self/instance)
    if len(args) > 1:  # args[0] is self/instance
        for i, arg in enumerate(args[1:], 1):
            # Convert argument to string and make it filename-safe
            arg_str = str(arg).replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
            filename_parts.append(f"arg{i}={arg_str}")

    # Add keyword arguments
    for key, value in kwargs.items():
        # Convert value to string and make it filename-safe
        value_str = str(value).replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')
        filename_parts.append(f"{key}={value_str}")

    # Join parts with underscores and add extension
    cache_filename = "___".join(filename_parts) + ".cache"

    # If filename is too long, use a hash for the parameters part
    if len(cache_filename) > 200:  # Windows filename limit is 255, leave some margin
        # Create hash of parameters
        param_str = "___".join(filename_parts[2:])  # Everything except sys_client and func_name
        param_hash = hashlib.md5(param_str.encode('utf-8')).hexdigest()[:16]
        cache_filename = f"{sys_client}___{func_name}___hash_{param_hash}.cache"

    return cache_filename


def cacheit(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        # First argument should be the instance (self)
        instance = args[0]

        # Create a unique cache filename based on sys_client, function name, and parameters
        __cache_filename = _generate_cache_filename(instance.sys_client, func.__name__, args, kwargs)
        __cache_filepath = os.path.join(paths.cache_dir, __cache_filename)

        # Check if the cache file exists and is not expired
        if os.path.exists(__cache_filepath):
            # Get file creation time
            file_creation_time = datetime.fromtimestamp(os.path.getctime(__cache_filepath))
            current_time = datetime.now()
            time_diff = (current_time - file_creation_time).total_seconds()
            
            # Check if cache is expired
            if time_diff > get_cache_expiration_seconds():
                logger.info(f"Cache file {__cache_filename} has expired, will be recreated.")
                try:
                    os.remove(__cache_filepath)
                except OSError as e:
                    logger.warning(f"Failed to remove expired cache file {__cache_filename}: {e}")
            else:
                try:
                    # If the cache file exists and is not expired, read the data from the file and return it
                    logger.info(f"The cache file {__cache_filename} is valid, skip RFC query.")
                    _df = pd.read_csv(__cache_filepath, na_filter=False)

                    logger.info(f'Read {_df.shape[0]} entries from {os.path.basename(__cache_filepath)}')
                    return _df
                except pd.errors.EmptyDataError:
                    # Log a warning if the cache file is empty
                    logger.warning(f"The file {__cache_filepath} is empty.")
                    try:
                        os.remove(__cache_filepath)
                    except OSError as e:
                        logger.warning(f"Failed to remove empty cache file {__cache_filename}: {e}")

        # If the cache file does not exist or is expired, execute the function, save the result to the cache file, and return it
        __df = func(*args, **kwargs)

        # Ensure cache directory exists
        os.makedirs(paths.cache_dir, exist_ok=True)
        __df.to_csv(__cache_filepath, index=False, na_rep='')
        logger.info(f'Created new cache file {__cache_filename} with {__df.shape[0]} entries')
        return __df

    return wrapper
