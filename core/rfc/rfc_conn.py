from common.config import config
from common.logger import logger
from pyrfc import Connection, LogonError
from typing import Dict, Optional, Any, TypedDict
from core.utils.util import timeit


class SapSystemConfig(TypedDict, total=False):
    """Type definition for SAP system configuration parameters."""
    # Application server parameters
    ashost: Optional[str]
    sysnr: Optional[str]
    # Load balancing parameters
    mshost: Optional[str]
    group: Optional[str]
    r3name: Optional[str]
    # Authentication parameters
    client: str
    user: str
    passwd: str


class RfcConn:
    """RFC Connection class for SAP systems.
    Handles connection to SAP systems using either application server or load balancing.
    """
    @staticmethod
    def get_system_config(sys_client: str) -> SapSystemConfig:
        """Get SAP system configuration from config file.
        
        Args:
            sys_client: SAP system client identifier
            
        Returns:
            SapSystemConfig: Configuration parameters for the SAP system
            
        Raises:
            ValueError: If system configuration is not found or required credentials are missing
        """
        # Get system configuration from YAML SystemConnection section
        sys_config = config.get('SystemConnection', {}).get(sys_client, {})
        if not sys_config:
            logger.fatal(f'System configuration not found for {sys_client}')
            raise ValueError(f"System configuration not found for {sys_client}")
            
        # Convert configuration to typed dictionary
        logon_params: SapSystemConfig = {}
        
        # Copy all configuration parameters
        for key, value in sys_config.items():
            if value is not None:  # Only copy non-None values
                logon_params[key] = value
        
        # Check if required credentials are available
        if not all([logon_params.get('user'), logon_params.get('client'), logon_params.get('passwd')]):
            logger.fatal(f'Missing required credentials for {sys_client}')
            raise ValueError(f"Missing required credentials for {sys_client}")
            
        return logon_params
    
    @timeit
    def __init__(self, sys_client: str):
        """Initialize RFC connection for the specified SAP system client.
        
        Args:
            sys_client: SAP system client identifier
        """
        self.sys_client = sys_client
        
        # Get system configuration
        _logon = self.get_system_config(sys_client)

        try:
            logger.info(f'Connecting to RFC destination {self.sys_client} ...')
            
            # Determine connection type based on available parameters
            if 'ashost' in _logon:
                # Application server connection
                connection_type = "Application Server"
            elif 'mshost' in _logon:
                # Load balancing connection
                connection_type = "Load Balancing"
            else:
                logger.fatal(f'No valid connection parameters for {self.sys_client}')
                raise ValueError("Missing required connection parameters (ashost or mshost)")
                
            # Create connection with all parameters at once
            self.conn = Connection(**_logon)
            logger.debug(f'Connected using {connection_type} mode')
                
        except LogonError as e:
            logger.fatal(f'SAP logon error: {e}')
            raise
        except Exception as e:
            logger.fatal(f'Unexpected error connecting to {self.sys_client}: {e}')
            raise
        else:
            logger.info(f'Successfully connected to {self.sys_client}!')

    def __repr__(self):
        """String representation of the RFC connection.
        
        Returns:
            String representation of the system client
        """
        return f"{self.sys_client}"


if __name__ == '__main__':
    # Example usage of RfcConn class
    rfc_conn = RfcConn('R8K.079')
    
    # Print connection information
    print(f"Connection: {rfc_conn}")
    
    # Print available attributes and methods of the RfcConn instance
    print("\nRfcConn attributes and methods:")
    for attr in sorted(dir(rfc_conn)):
        if not attr.startswith('__'):
            print(f"  - {attr}")
    
    # Print available attributes and methods of the underlying pyrfc Connection
    print("\nConnection attributes and methods:")
    for attr in sorted(dir(rfc_conn.conn)):
        if not attr.startswith('__'):
            print(f"  - {attr}")
