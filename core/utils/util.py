import re
import time
from ast import literal_eval
from functools import wraps
from typing import Callable, TypeVar, Any, Optional

import pandas as pd
from common.logger import logger

# Type variables for better type hinting
T = TypeVar('T')
DF = TypeVar('DF', bound=pd.DataFrame)


def timeit(func: Callable[..., T]) -> Callable[..., T]:
    """Decorator that logs the execution time of the decorated function.
    
    Args:
        func: The function to be timed
        
    Returns:
        A wrapped function that logs execution time
    """
    @wraps(func)
    def timeit_wrapper(*args: Any, **kwargs: Any) -> T:
        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        total_time = end_time - start_time
        logger.debug(
            f"Execution of the function {func.__name__} took {total_time:.4f} seconds"
        )
        return result

    return timeit_wrapper


def lowercase_column_names(func: Callable[..., DF]) -> Callable[..., DF]:
    """Decorator that converts all DataFrame column names to lowercase.
    
    This decorator is designed to be used with functions that return a pandas DataFrame.
    It automatically converts all column names to lowercase after the function executes.
    
    Args:
        func: The function that returns a DataFrame
        
    Returns:
        A wrapped function that returns a DataFrame with lowercase column names
    """
    @wraps(func)
    def wrapper(*args: Any, **kwargs: Any) -> DF:
        df = func(*args, **kwargs)
        if isinstance(df, pd.DataFrame):
            df.columns = [col.lower() for col in df.columns]
        return df

    return wrapper
