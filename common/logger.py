import os
import logging
import coloredlogs
from datetime import datetime, date
from logging.handlers import RotatingFileHandler


# 日志级别
CRITICAL = 50
FATAL = CRITICAL
ERROR = 40
WARNING = 30
WARN = WARNING
INFO = 20
DEBUG = 10
NOTSET = 0

LOG_NAME = 'starter_pack_comparison'

current_path = os.path.dirname(os.path.abspath(__file__))
root_path = os.path.join(current_path, '..')
log_dir = os.path.join(root_path, "logs")

field_styles = {
    "asctime": {"color": "white", "bright": False},
    "filename": {"color": "white", "bright": False},
    "levelname": {"color": "white", "bright": False},
    "threadName": {"color": "white", "bright": False},
    "lineno": {"color": "white", "bright": False},
}

level_styles = {
    "critical": {"bold": False, "color": "red", "inverse": False},
    "debug": {"color": "black", "bright": True},
    "error": {"color": "red", "faint": True},
    "info": {"color": "white", "bright": True},
    "notice": {"color": "magenta"},
    "spam": {"color": "green", "faint": True},
    "success": {"bold": True, "color": "green", "bright": True},
    "verbose": {"color": "blue"},
    "warning": {"color": "yellow", "bright": False},
}

# Create a logger object.
# logger = logging.getLogger(__name__)
logger = logging.getLogger(f'{LOG_NAME}')

# console
coloredlogs.install(
    # fmt="%(asctime)s,%(msecs)03d %(threadName)-15s  %(filename)17s line:%(lineno)4d %(levelname)8s %(message)s",
    fmt="%(asctime)s %(filename)17s line:%(lineno)4d %(levelname)8s %(message)s",
    # alert_level=logging.INFO,
    level=logging.DEBUG,
    level_styles=level_styles,
    field_styles=field_styles,
)

# file
today = date.today()
log_file = f"{LOG_NAME}_{datetime.strftime(today, "%Y_%m_%d")}.log"
log_file_path = os.path.join(log_dir, log_file)

fmt = logging.Formatter(
    fmt="%(asctime)s,%(msecs)03d %(threadName)-15s  %(filename)17s line:%(lineno)4d %(levelname)8s %(message)s",
)
# fh = logging.FileHandler(log_file_path, mode="a", encoding='utf-8')
fh = RotatingFileHandler(
    log_file_path, maxBytes=1 * 1024 * 1024, backupCount=2, encoding="utf-8"
)

fh.setLevel(logging.DEBUG)
fh.setFormatter(fmt)
logger.addHandler(fh)

if __name__ == "__main__":
    logger.info("this is a tests msg with Unicode \u6cbb")
    logger.critical("this is a tests log in 中文")
    logger.fatal("this is a tests log in 中文")
    logger.error("this is a tests log in 中文")
    logger.warning("this is a tests log in 中文")
    logger.debug("this is a tests log in 中文")
