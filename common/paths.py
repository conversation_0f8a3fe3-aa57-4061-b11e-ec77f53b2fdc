from pathlib import Path

class Paths:
    """
    Centralized path management for the project
    
    Implements singleton pattern using decorator to ensure single instance
    """
    
    def __init__(self):
        """Initialize all paths"""
        # Project root directory
        root_path = Path(__file__).resolve().parent.parent

        # Log directory
        self.log_dir = root_path / "logs"

        # Config directories
        self._config_dir = root_path / "config"

        # Config files
        self.config_file = self._config_dir / "config.yaml"
        self.system_client_file = self._config_dir / "system_client.yaml"


        # Data directory
        self.data_dir = root_path / "data"
        self.cache_dir = root_path / 'data' / ".cache"
        self.vfile_dir = root_path / 'data' / "vfile"

# 创建全局单例实例
paths = Paths()

if __name__ == "__main__":
    # Print all paths
    paths = Paths()
    print("Log directory:", paths.log_dir)
    print("Data directory:", paths.data_dir)
    print("Config file:", paths.config_file)
    print("V-File file:", paths.vfile_dir)

