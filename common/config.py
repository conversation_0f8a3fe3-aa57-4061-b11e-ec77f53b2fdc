from pathlib import Path
import yaml
from typing import Dict, List, Any, Optional

from common.paths import paths

# Load main configuration
with open(paths.config_file, 'r', encoding='utf-8') as file:
    config = yaml.safe_load(file)

# Load system client configuration
with open(paths.system_client_file, 'r', encoding='utf-8') as file:
    system_client_config = yaml.safe_load(file)

# Merge configurations
if system_client_config:
    # Add system client configuration to SystemConnection node
    config['SystemConnection'] = system_client_config

def get_source_country_code() -> str:
    """Get the source country code.
    
    Reads the source country code (referenceCountry) from the configuration file.
    
    Returns:
        str: The source country code
    """
    return config['countryComparison']['referenceCountry']

def get_starter_pack_country_codes() -> List[str]:
    """Get a list of starter pack country codes.
    
    Reads the target countries (comparisonCountries) from the configuration file.
    
    Returns:
        List[str]: A list of starter pack country codes
    """
    return config['countryComparison']['comparisonCountries']

def get_all_country_codes() -> List[str]:
    """Get a list of all country codes to be processed.
    
    Reads the source country (referenceCountry) and target countries (comparisonCountries) from the
    configuration file and combines them into a complete list of country codes.
    
    Returns:
        List[str]: A list containing the source country and all target country codes
    """
    source_country = get_source_country_code()
    starter_pack_countries = get_starter_pack_country_codes()
    
    # Ensure source country is at the beginning of the list
    all_countries = [source_country] + starter_pack_countries
    return all_countries

def get_system_client() -> str:
    """Get system client from config.
    
    Reads the system client from the default section of the configuration file.
    
    Returns:
        str: The system client identifier
    """
    return config['default']['system_client']

def get_deprecated_scope_items() -> List[str]:
    """Get deprecated scope items from config.
    
    Reads the list of deprecated scope items from the configuration file.
    
    Returns:
        List[str]: A list of deprecated scope items
    """
    return config['deprecatedScopeItem']

def get_perforce_config() -> Dict[str, str]:
    """Get Perforce configuration.
    
    Reads the Perforce settings from the configuration file.
    
    Returns:
        Dict[str, str]: A dictionary containing Perforce configuration
    """
    return config['perforce']

def get_cache_expiration_seconds() -> int:
    """Get cache expiration time in seconds.

    Reads the cache expiration time from the configuration file.

    Returns:
        int: Cache expiration time in seconds
    """
    return config['cacheExpiration']['seconds']

def get_solution_pattern(sys_client: str) -> str:
    """Get solution pattern for a specific system client.

    Reads the solution pattern configuration for the specified system client.

    Args:
        sys_client: SAP system client identifier

    Returns:
        str: The solution pattern type ('cloud_enterprise' or 'onprem_hana')

    Raises:
        ValueError: If system configuration is not found or pattern is not specified
    """
    # Get system configuration from SystemConnection section
    sys_config = config.get('SystemConnection', {}).get(sys_client, {})
    if not sys_config:
        raise ValueError(f"System configuration not found for {sys_client}")

    pattern = sys_config.get('solution_pattern')
    if not pattern:
        raise ValueError(f"Solution pattern not configured for {sys_client}")

    if pattern not in ['cloud_enterprise', 'onprem_hana']:
        raise ValueError(f"Invalid solution pattern '{pattern}' for {sys_client}. "
                        f"Must be 'cloud_enterprise' or 'onprem_hana'")

    return pattern