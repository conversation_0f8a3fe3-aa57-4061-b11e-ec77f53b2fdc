class Singleton:
    """Singleton decorator class (corrected version)"""
    def __init__(self, cls):
        self._cls = cls
        self._instance = None
        # Key: preserve reference to original class
        self.__wrapped__ = cls  # Add __wrapped__ attribute

    def __call__(self, *args, **kwargs):
        if self._instance is None:
            self._instance = self._cls(*args, **kwargs)
        return self._instance


