from typing import Any

import re
import pandas as pd
from enum import Enum
# Use country code list from configuration for filtering
from common.config import get_all_country_codes
from common.config import config, get_solution_pattern
from common.logger import logger
from core.rfc.rfc_query import RfcQuery
from core.rfc.cacheit import cacheit
from core.utils.util import lowercase_column_names

class ZeroEntriesError(Exception):
    """Exception raised when query returns zero entries"""
    pass


class SQLTable(Enum):
    """Enumeration of SQL table paths used in the solution build process"""
    BB_PRJ_SCO = '/SMB/BB_PRJ_SCO'
    BB_PRJ_ET01 = '/SMB/BB_PRJ_ET01'
    BBPR_I = '/SMB/BBPR_I'
    BB_PRJ_H = '/SMB/BB_PRJ_H'
    BB_LIB_I = '/SMB/BB_LIB_I'
    BB_IMG_SOBJ = '/SMB/BB_IMG_SOBJ'
    SOBJ_DEF = '/SMB/SOBJ_DEF'
    SOBJ_DEF_I = '/SMB/SOBJ_DEF_I'
    TASK_CRIT = '/SMB/TASK_CRIT'
    ECAT_DEF = '/SMB/ECAT_DEF'  # ECAT definition table
    SB_T_005 = '/SMB/SB_T_005'  # SB_T_005 table


class SolBuild(RfcQuery):
    """Solution Build class for querying and processing SAP solution data"""

    def __init__(self, sys_client):
        """Initialize SolBuild with system client"""
        super().__init__(sys_client)

    def __del__(self):
        """Destructor for cleanup operations"""
        logger.debug(f'__del__ function is called')

    # ========== Utility Methods ==========

    def _is_all_countries(self, land1: str) -> bool:
        """
        Check if the query is for all countries

        Args:
            land1: Country code parameter

        Returns:
            bool: True if querying all countries
        """
        return land1.lower() in ['*', 'all', '']

    def _validate_query_result(self, df_result: pd.DataFrame, sql_table: str) -> None:
        """
        Validate query result is not empty

        Args:
            df_result: Query result dataframe
            sql_table: Name of the queried table

        Raises:
            ZeroEntriesError: When query result is empty
        """
        result_count = len(df_result)
        logger.info(f'{self.sys_client}: Retrieved {result_count} records from table {sql_table}')

        if result_count > 0:
            logger.info(f'{self.sys_client}: {result_count} records remaining after data cleaning')
        else:
            error_msg = f'System {self.sys_client} table {sql_table} cannot have 0 records'
            logger.fatal(error_msg)
            raise ZeroEntriesError(error_msg)

    # ========== WHERE Condition Builders ==========

    def _build_where_conditions_for_solution_header(self, pattern_type: str, land1: str) -> list[str]:
        """
        Build WHERE query conditions based on pattern type and country code

        Args:
            pattern_type: Solution pattern type ('cloud_enterprise' or 'onprem_hana')
            land1: Country code

        Returns:
            list[str]: List of WHERE conditions

        Raises:
            ValueError: When pattern type is invalid
        """
        # Define SQL LIKE patterns
        patterns = {
            'cloud_enterprise': 'BP#_CLD#_ENTPR#_S4CLD%#_%V%',
            'onprem_hana': 'BP#_S4BL#_S4HANAX#_%V1'
        }

        if pattern_type not in patterns:
            raise ValueError(f"Invalid pattern type '{pattern_type}' for {self.sys_client}")

        sql_pattern = patterns[pattern_type]
        base_condition = f"SCOPE LIKE '{sql_pattern}' ESCAPE '#'"

        # Check if country code filter is needed
        if self._is_all_countries(land1):
            return [base_condition]
        else:
            return [f"{base_condition} AND LAND1 = '{land1}'"]

    def _build_where_conditions_for_scopeitem_to_solution(self, pattern_type: str, land1: str) -> list[str]:
        """
        Build WHERE query conditions for scope item to solution mapping

        Args:
            pattern_type: Solution pattern type ('cloud_enterprise' or 'onprem_hana')
            land1: Country code

        Returns:
            list[str]: List of WHERE conditions

        Raises:
            ValueError: When pattern type is invalid
        """
        # Define SQL LIKE patterns
        sql_like_solution_patterns = {
            'cloud_enterprise': 'BP#_CLD#_ENTPR#_S4CLD%#_%V%' if self._is_all_countries(land1) else f'BP#_CLD#_ENTPR#_S4CLD%#{land1}V%',
            'onprem_hana': 'BP#_S4BL#_S4HANAX#_%V1' if self._is_all_countries(land1) else f'BP#_S4BL#_S4HANAX#_{land1}V1'
        }

        if pattern_type not in sql_like_solution_patterns:
            raise ValueError(f"Invalid pattern type '{pattern_type}' for {self.sys_client}")

        sql_like_pattern = sql_like_solution_patterns[pattern_type]

        # Return WHERE condition
        return [f"SCOPE LIKE '{sql_like_pattern}' ESCAPE '#'"]

    def _build_where_conditions_for_bb_to_scopeitem(self, land1):
        """
        Build WHERE conditions for building block to scope item mapping

        Args:
            land1: Country code

        Returns:
            list[str]: List of WHERE conditions
        """
        if self._is_all_countries(land1):
            return [r"PRJID LIKE '%#_%' ESCAPE '#' AND OBJTY = 'BBK' AND BBID LIKE '% (__)%'"]
        else:
            # Explicit grouping logic & remove redundant spaces
            # see CL_RFC_READ_TABLE=============CM002, line 16
            return [rf"( PRJID LIKE '{land1}#_%' ESCAPE '#' OR PRJID LIKE 'XX#_%' ESCAPE '#' )", # Trailing space will be removed automatically
                    rf" AND OBJTY = 'BBK'", # Leading space will be kept
                    rf" AND ( BBID LIKE '% ({land1})%' OR BBID LIKE '% (XX)%' )"]   # Leading space will be kept

    # ========== Core Data Retrieval Methods ==========

    @cacheit
    @lowercase_column_names
    def get_df_solution_header(self, land1: str = '*') -> pd.DataFrame:
        """
        Get solution header data table information

        Args:
            land1: Solution country code, default '*' means all countries

        Returns:
            pd.DataFrame: Dataframe containing SCOPE and LAND1 fields

        Raises:
            ValueError: When system configuration pattern type is invalid
            ZeroEntriesError: When query result is empty
        """
        sql_table = SQLTable.BB_PRJ_SCO.value
        logger.info(f'{self.sys_client}: Starting to fetch data from table `{sql_table}`...')

        # Get system configured solution pattern type
        pattern_type = get_solution_pattern(self.sys_client)

        # Build query conditions
        where_conditions = self._build_where_conditions_for_solution_header(pattern_type, land1)

        # Execute query
        df_result = self.query_as_df(
            fields=['SCOPE', 'LAND1'],
            sql_table=sql_table,
            where=where_conditions
        )

        # Validate query result
        self._validate_query_result(df_result, sql_table)

        return df_result



    @cacheit
    @lowercase_column_names
    def get_df_scopeitem_to_solution(self, land1: str = '*') -> pd.DataFrame:
        """
        Get scope item to solution mapping data

        Args:
            land1: Country code, default '*' means all countries

        Returns:
            pd.DataFrame: Dataframe containing scope and prjid fields
        """
        _sql_table = SQLTable.BB_PRJ_ET01.value
        logger.info(f'{self.sys_client}: fetch entries from the table {_sql_table}...')

        # Get system configured solution pattern type
        pattern_type = get_solution_pattern(self.sys_client)

        # Build query conditions
        where_conditions = self._build_where_conditions_for_scopeitem_to_solution(pattern_type, land1)

        scope_item_to_solution = self.query_as_df(fields=['scope', 'prjid'], sql_table=_sql_table,
                                                  where=where_conditions)

        # Exclude deprecated scope items 'DE_1SG', 'US_1SG' ## TODO: This logic is not yet connected, add directly to SQL (may have length issues), or filter
        # scope_item_to_solution = scope_item_to_solution[
        #     ~scope_item_to_solution['prjid'].isin(config['deprecatedScopeItem'])]

        logger.info(
            f'{self.sys_client}: a total of {len(scope_item_to_solution)} entries fetched from the table {_sql_table}')

        _length = len(scope_item_to_solution)
        if _length > 0:
            logger.info(f'{self.sys_client}: after data cleaning, the data obtained from {_sql_table}, '
                        f'there are {len(scope_item_to_solution)} entries remaining.')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return scope_item_to_solution

    @cacheit
    @lowercase_column_names
    def get_df_bb_to_scopeitem(self, land1: str = '*') -> pd.DataFrame:
        """
        Get building block to scope item mapping data

        Args:
            land1: Country code, default '*' means all countries

        Returns:
            pd.DataFrame: Dataframe containing prjid and bbid fields
        """
        # /SMB/BBPR_I - Building block project entries
        _sql_table = SQLTable.BBPR_I.value
        logger.info(f'{self.sys_client}: fetch data from the table {_sql_table}...')

        where_conditions = self._build_where_conditions_for_bb_to_scopeitem(land1)

        bb_to_scope_item = self.query_as_df(fields=['prjid', 'bbid'], sql_table=_sql_table,
                                            where=where_conditions
                                            )

        logger.info(f'{self.sys_client}: {len(bb_to_scope_item)} entries fetched from the table {_sql_table}')

        # Data cleaning
        # Pattern for matching scope item IDs
        # Format: 2 alphanumeric characters + underscore + 3 alphanumeric characters (e.g., "AB_123", "DE_BD3")
        _pattern = r'^[A-Z0-9]{2}_[A-Z0-9]{3}$'
        bb_to_scope_item = bb_to_scope_item[(bb_to_scope_item['prjid'].str.match(_pattern))]

        _length = len(bb_to_scope_item)
        if _length > 0:
            logger.info(f'{self.sys_client}: after data cleaning, the data obtained from {_sql_table}, '
                        f'there are {len(bb_to_scope_item)} entries remaining.')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return bb_to_scope_item

    @cacheit
    @lowercase_column_names
    def get_df_bb_lib_item(self) -> pd.DataFrame:
        """
        Get building block library item data

        Returns:
            pd.DataFrame: Dataframe containing BB library information
        """
        # BB library I table
        _sql_table = SQLTable.BB_LIB_I.value
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        _df_bb_lib_i = self.query_as_df(fields=['BBID', 'SEQNUM', 'OBJTY', 'OBJID', 'FILENAME',
                                                'FIELDNAME', 'ALTN_OBJTY', 'ALTN_OBJID'],
                                        sql_table=_sql_table)

        logger.info(f'{self.sys_client}: {len(_df_bb_lib_i)} entries fetched from the table {_sql_table}')

        _length = len(_df_bb_lib_i)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return _df_bb_lib_i

    @cacheit
    @lowercase_column_names
    def get_df_bb_img_sobj(self) -> pd.DataFrame:
        """
        Get building block image sub-object data

        Returns:
            pd.DataFrame: Dataframe containing BB image sub-object information
        """
        # BB image sub-object table
        _sql_table = SQLTable.BB_IMG_SOBJ.value
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        _df_bb_img_sobj = self.query_as_df(fields=['TASK_ID', 'OBJID', 'OBJTY', 'BBID', 'IMG_ACTIVITY',
                                                   'CUST_OBJTYPE', 'CUST_OBJNAME', 'SUBOBJNAME', 'FILENAME'],
                                           sql_table=_sql_table,
                                           where=[r"STATUS = 'A'"])

        logger.info(f'{self.sys_client}: {len(_df_bb_img_sobj)} entries fetched from the table {_sql_table}')

        _length = len(_df_bb_img_sobj)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return _df_bb_img_sobj

    @cacheit
    @lowercase_column_names
    def get_df_sobj_def(self) -> pd.DataFrame:
        """
        Get sub-object definition data from SOBJ_DEF table

        Returns:
            pd.DataFrame: Dataframe containing sub-object definition information
        """
        # Get sub-object definition base table
        _sql_table = SQLTable.SOBJ_DEF.value
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        df_sobj_def = self.query_as_df(fields=['IMG_ACTIVITY', 'CUST_OBJTYPE', 'CUST_OBJNAME', 'SUBOBJNAME', 'GUID'],
                                       # Verified that FILENAME is all empty
                                       sql_table=_sql_table,
                                       where=["VERSION = '000000'"])
        logger.info(f'{self.sys_client}: {len(df_sobj_def)} entries fetched from the table {_sql_table}')

        return df_sobj_def

    @cacheit
    @lowercase_column_names
    def get_df_sobj_def_i(self) -> pd.DataFrame:
        """
        Get sub-object definition item data from SOBJ_DEF_I table

        Returns:
            pd.DataFrame: Dataframe containing sub-object definition item information
        """
        # Sub-object definition table
        _sql_table = SQLTable.SOBJ_DEF_I.value
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        _df_sobj_def_i = self.query_as_df(fields=['GUID', 'FIELDNAME', 'PARAM', 'KEYFLAG', 'CENTRALDATA'],
                                          sql_table=_sql_table,
                                          where=["VERSION = '000000'"])

        logger.info(f'{self.sys_client}: {len(_df_sobj_def_i)} entries fetched from the table {_sql_table}')

        _length = len(_df_sobj_def_i)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return _df_sobj_def_i

    @cacheit
    @lowercase_column_names
    def get_df_ecat_def(self) -> pd.DataFrame:
        """
        Get ECAT definition data from ECAT_DEF table

        Returns:
            pd.DataFrame: Dataframe containing ECAT definition information
        """
        # Get ECAT definition table
        _sql_table = SQLTable.ECAT_DEF.value
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        df_ecat_def = self.query_as_df(fields=['OBJID'],
                                       sql_table=_sql_table,
                                       where=["VERSION = '000000' AND GMD_STATUS <> 'D'"])
        logger.info(f'{self.sys_client}: {len(df_ecat_def)} entries fetched from the table {_sql_table}')

        _length = len(df_ecat_def)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return df_ecat_def

    @cacheit
    @lowercase_column_names
    def get_df_sb_t_005(self) -> pd.DataFrame:
        """
        Get data from SB_T_005 table

        Returns:
            pd.DataFrame: Dataframe containing SB_T_005 information
        """
        # Get SB_T_005 table
        _sql_table = SQLTable.SB_T_005.value
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        df_sb_t_005 = self.query_as_df(fields=['OBJID', 'VERSION', 'OBJTY', 'PNAME', 'PTYP', 'KEYFLAG'],
                                       sql_table=_sql_table,
                                       where=["VERSION = '000000' AND PTYP = 'I'"])
        logger.info(f'{self.sys_client}: {len(df_sb_t_005)} entries fetched from the table {_sql_table}')

        _length = len(df_sb_t_005)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return df_sb_t_005

    @cacheit
    @lowercase_column_names
    def get_df_bc_def_info(self) -> pd.DataFrame:
        """
        Get BCSET_ID data from BC_DEF_INFO table

        Returns:
            pd.DataFrame: Dataframe containing BC definition information
        """
        # Get BC_DEF_INFO table
        _sql_table = '/SMB/BC_DEF_INFO'
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        df_bc_def_info = self.query_as_df(fields=['BCSET_ID'],
                                          sql_table=_sql_table,
                                          where=["VERSION = '000000' AND GMD_STATUS <> 'D'"])
        logger.info(f'{self.sys_client}: {len(df_bc_def_info)} entries fetched from the table {_sql_table}')

        _length = len(df_bc_def_info)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return df_bc_def_info

    @cacheit
    @lowercase_column_names
    def get_df_bc_variant(self) -> pd.DataFrame:
        """
        Get BC variant data from BC_VARIANT table

        Returns:
            pd.DataFrame: Dataframe containing BC variant information
        """
        # Get BC_VARIANT table
        _sql_table = '/SMB/BC_VARIANT'
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        df_bc_variant = self.query_as_df(fields=['BCSET_ID', 'VERSION', 'BCSET', 'TABLENAME', 'FIELDNAME', 'PARAM'],
                                         sql_table=_sql_table)
        logger.info(f'{self.sys_client}: {len(df_bc_variant)} entries fetched from the table {_sql_table}')

        _length = len(df_bc_variant)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return df_bc_variant

    @cacheit
    @lowercase_column_names
    def get_df_bc_def_fields(self) -> pd.DataFrame:
        """
        Get BC definition fields data from BC_DEF_FIELDS table

        Returns:
            pd.DataFrame: Dataframe containing BC definition fields information
        """
        # Get BC_DEF_FIELDS table
        _sql_table = '/SMB/BC_DEF_FIELDS'
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        df_bc_def_fields = self.query_as_df(fields=['BCSET_ID', 'VERSION', 'BCSET', 'TABLENAME', 'FIELDNAME', 'PARAM'],
                                            sql_table=_sql_table,
                                            where=["VERSION = '000000' AND GMD_STATUS <> 'D'"]
                                            )
        logger.info(f'{self.sys_client}: {len(df_bc_def_fields)} entries fetched from the table {_sql_table}')

        _length = len(df_bc_def_fields)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return df_bc_def_fields

    @cacheit
    @lowercase_column_names
    def get_df_field_info(self) -> pd.DataFrame:
        """
        Get field info data from FIELD_INFO table

        Returns:
            pd.DataFrame: Dataframe containing field information
        """
        # Get FIELD_INFO table
        _sql_table = '/SMB/FIELD_INFO'
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        df_field_info = self.query_as_df(fields=['TABLENAME', 'FIELDNAME', 'KEYFLAG'],
                                         sql_table=_sql_table)
        logger.info(f'{self.sys_client}: {len(df_field_info)} entries fetched from the table {_sql_table}')

        _length = len(df_field_info)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return df_field_info

    @cacheit
    @lowercase_column_names
    def get_df_task_crit_upgrade(self) -> pd.DataFrame:
        """
        Get task criteria data for upgrade-relevant entries

        Returns:
            pd.DataFrame: Dataframe containing task criteria information
        """
        # Task criteria table - upgrade related
        _sql_table = SQLTable.TASK_CRIT.value
        logger.info(f'{self.sys_client}: fetch entries from the table `{_sql_table}`...')
        _df_task_crit = self.query_as_df(fields=['CRITERION', 'OBJTY', 'OBJID', 'FILENAME', 'BBID'],
                                         sql_table=_sql_table,
                                         where=[
                                             "CRITERION = 'UPGRADE_RELEVANT' AND VERSION = '000000' AND FILENAME = '' AND GMD_STATUS <> 'D'"])

        logger.info(f'{self.sys_client}: {len(_df_task_crit)} entries fetched from the table {_sql_table}')

        _length = len(_df_task_crit)
        if _length > 0:
            logger.info(f'{self.sys_client}: {_length} entries fetched successfully from {_sql_table}')
        else:
            logger.fatal(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')
            raise ZeroEntriesError(f'In {self.sys_client}, there cannot be 0 entries in table {_sql_table}')

        return _df_task_crit

    # ========== Data Processing and Joining Methods ==========

    def join_dataframes(self) -> tuple[Any, Any, Any]:
        """
        Join multiple dataframes to create comprehensive solution build data

        Returns:
            tuple[Any, Any, Any]: Tuple containing IMG, ECAT, and BCS dataframes
        """
        # Step 1: Filter solution header by country codes
        df_solution_header = self.get_df_solution_header()
        df_solution_header = df_solution_header[df_solution_header['land1'].isin(get_all_country_codes())]

        # Step 2: Inner join with scope item to solution mapping on scope
        df_scopeitem_to_solution = self.get_df_scopeitem_to_solution()
        df_joined = pd.merge(df_solution_header, df_scopeitem_to_solution, on='scope', how='inner')

        # Step 3: Inner join with building block to scope item mapping on prjid
        df_bb_to_scopeitem = self.get_df_bb_to_scopeitem()

        # Filter bbid to only include entries with specific country codes
        valid_country_codes = r'\((?:DE|BS|CW|CY|GG|JE|KY|VG)\)'
        df_bb_to_scopeitem = df_bb_to_scopeitem[
            df_bb_to_scopeitem['bbid'].str.contains(valid_country_codes, regex=True, flags=re.IGNORECASE)]
        df_joined = pd.merge(df_joined, df_bb_to_scopeitem, on='prjid', how='inner')

        # Step 4: Inner join with building block library item on BBID
        df_bb_lib_item = self.get_df_bb_lib_item()
        df_joined = pd.merge(df_joined, df_bb_lib_item, on='bbid', how='inner')  # filename comes from bb_lib_i

        # Rename columns for clarity
        df_joined = df_joined.rename(columns={
            'objty': 'activity_type',
            'objid': 'activity_id'
        })  # counted 353206

        # Split dataframe based on activity type values
        df_joined_img = df_joined[df_joined['activity_type'] == 'IMG'].copy()  # counted 204153
        df_joined_ecat_bcs = df_joined[
            (df_joined['activity_type'] == 'ECAT') | (df_joined['activity_type'] == 'BCS')].copy()  # Merge ECAT and BCS types

        # Remove prjid column and drop duplicates for each dataframe
        df_joined_img = df_joined_img.drop('prjid', axis=1).drop_duplicates().sort_index()
        df_joined_ecat_bcs = df_joined_ecat_bcs.drop('prjid', axis=1).drop_duplicates().sort_index()

        # Step 5a: Inner join with task criteria upgrade data on BBID, OBJTY, OBJID
        df_task_crit_upgrade = self.get_df_task_crit_upgrade()

        # Treat empty BBID as a wildcard (global criteria, not BBID level)
        df_task_crit_upgrade_wildcard = df_task_crit_upgrade[
            df_task_crit_upgrade['bbid'].isna() | (df_task_crit_upgrade['bbid'] == '') | (
                        df_task_crit_upgrade['bbid'].str.strip() == '')]
        df_task_crit_upgrade_normal = df_task_crit_upgrade[
            df_task_crit_upgrade['bbid'].notna() & (df_task_crit_upgrade['bbid'] != '') & (
                        df_task_crit_upgrade['bbid'].str.strip() != '')]

        # Process normal and wildcard data separately
        # Join with normal BBID
        df_joined_img_normal = pd.merge(df_joined_img, df_task_crit_upgrade_normal,
                                        left_on=['bbid', 'activity_type', 'activity_id'],
                                        right_on=['bbid', 'objty', 'objid'],
                                        how='inner',
                                        suffixes=('', '_criteria'))  # bbid duplicate

        # Join with wildcard BBID
        df_joined_img_wildcard = pd.merge(df_joined_img, df_task_crit_upgrade_wildcard,
                                          left_on=['activity_type', 'activity_id'],
                                          right_on=['objty', 'objid'],
                                          how='inner',
                                          suffixes=('', '_criteria'))  # bbid duplicate

        # Remove bbid_criteria columns from wildcard results
        df_joined_img_wildcard = df_joined_img_wildcard.drop(['bbid_criteria'], axis=1)

        # Remove matched records from original dataframe
        matched_records = pd.concat([df_joined_img_normal, df_joined_img_wildcard], ignore_index=True)

        # Create a mask by comparing the actual data instead of just indices
        # First, get the columns that uniquely identify a record
        key_columns = ['scope', 'land1', 'bbid', 'seqnum', 'activity_type', 'activity_id', 'filename', 'fieldname',
                       'altn_objty', 'altn_objid']

        # Create a mask for records that are not in matched_records by comparing the key columns
        mask = ~df_joined_img[key_columns].apply(tuple, axis=1).isin(
            matched_records[key_columns].apply(tuple, axis=1)
        )

        # Keep only unmatched records
        df_joined_img = df_joined_img[mask].copy()

        # Step 5b: Inner join with task criteria upgrade data for ECAT and BCS records
        # Split task criteria upgrade data into different categories based on bbid and filename wildcards
        df_task_crit_upgrade_ecat_bcs = df_task_crit_upgrade[
            (df_task_crit_upgrade['objty'].isin(['ECAT', 'BCS']))
        ]

        # Case 1: Both bbid and filename are specific (no wildcards)
        df_task_crit_upgrade_both_specific = df_task_crit_upgrade_ecat_bcs[
            df_task_crit_upgrade_ecat_bcs['bbid'].notna() &
            (df_task_crit_upgrade_ecat_bcs['bbid'] != '') &
            (df_task_crit_upgrade_ecat_bcs['bbid'].str.strip() != '') &
            df_task_crit_upgrade_ecat_bcs['filename'].notna() &
            (df_task_crit_upgrade_ecat_bcs['filename'] != '') &
            (df_task_crit_upgrade_ecat_bcs['filename'].str.strip() != '')
            ]

        # Case 2: bbid is wildcard but filename is specific
        df_task_crit_upgrade_bbid_wildcard = df_task_crit_upgrade_ecat_bcs[
            (df_task_crit_upgrade_ecat_bcs['bbid'].isna() |
             (df_task_crit_upgrade_ecat_bcs['bbid'] == '') |
             (df_task_crit_upgrade_ecat_bcs['bbid'].str.strip() == '')) &
            df_task_crit_upgrade_ecat_bcs['filename'].notna() &
            (df_task_crit_upgrade_ecat_bcs['filename'] != '') &
            (df_task_crit_upgrade_ecat_bcs['filename'].str.strip() != '')
            ]

        # Case 3: filename is wildcard but bbid is specific
        df_task_crit_upgrade_filename_wildcard = df_task_crit_upgrade_ecat_bcs[
            df_task_crit_upgrade_ecat_bcs['bbid'].notna() &
            (df_task_crit_upgrade_ecat_bcs['bbid'] != '') &
            (df_task_crit_upgrade_ecat_bcs['bbid'].str.strip() != '') &
            (df_task_crit_upgrade_ecat_bcs['filename'].isna() |
             (df_task_crit_upgrade_ecat_bcs['filename'] == '') |
             (df_task_crit_upgrade_ecat_bcs['filename'].str.strip() == ''))
            ]

        # Case 4: Both bbid and filename are wildcards
        df_task_crit_upgrade_both_wildcard = df_task_crit_upgrade_ecat_bcs[
            (df_task_crit_upgrade_ecat_bcs['bbid'].isna() |
             (df_task_crit_upgrade_ecat_bcs['bbid'] == '') |
             (df_task_crit_upgrade_ecat_bcs['bbid'].str.strip() == '')) &
            (df_task_crit_upgrade_ecat_bcs['filename'].isna() |
             (df_task_crit_upgrade_ecat_bcs['filename'] == '') |
             (df_task_crit_upgrade_ecat_bcs['filename'].str.strip() == ''))
            ]

        # Process each case

        # Case 1: Both specific
        df_joined_ecat_bcs_case1 = pd.merge(
            df_joined_ecat_bcs,
            df_task_crit_upgrade_both_specific,
            left_on=['activity_type', 'activity_id', 'bbid', 'filename'],
            right_on=['objty', 'objid', 'bbid', 'filename'],
            how='inner',
            suffixes=('', '_criteria')
        )

        # Case 2: bbid wildcard
        df_joined_ecat_bcs_case2 = pd.merge(
            df_joined_ecat_bcs,
            df_task_crit_upgrade_bbid_wildcard,
            left_on=['activity_type', 'activity_id', 'filename'],
            right_on=['objty', 'objid', 'filename'],
            how='inner',
            suffixes=('', '_criteria')
        )
        df_joined_ecat_bcs_case2 = df_joined_ecat_bcs_case2.drop(['bbid_criteria'], axis=1)

        # Case 3: filename wildcard
        df_joined_ecat_bcs_case3 = pd.merge(
            df_joined_ecat_bcs,
            df_task_crit_upgrade_filename_wildcard,
            left_on=['activity_type', 'activity_id', 'bbid'],
            right_on=['objty', 'objid', 'bbid'],
            how='inner',
            suffixes=('', '_criteria')
        )
        df_joined_ecat_bcs_case3 = df_joined_ecat_bcs_case3.drop(['filename_criteria'], axis=1)

        # Case 4: Both wildcard
        df_joined_ecat_bcs_case4 = pd.merge(
            df_joined_ecat_bcs,
            df_task_crit_upgrade_both_wildcard,
            left_on=['activity_type', 'activity_id'],
            right_on=['objty', 'objid'],
            how='inner',
            suffixes=('', '_criteria')
        )
        df_joined_ecat_bcs_case4 = df_joined_ecat_bcs_case4.drop(['bbid_criteria', 'filename_criteria'], axis=1)

        # Remove matched records from original dataframe
        matched_records = pd.concat([
            df_joined_ecat_bcs_case1,
            df_joined_ecat_bcs_case2,
            df_joined_ecat_bcs_case3,
            df_joined_ecat_bcs_case4
        ], ignore_index=True)
        # Create a mask for records that are not in matched_records
        mask = ~df_joined_ecat_bcs.index.isin(matched_records.index)
        # Keep only unmatched records
        df_joined_ecat_bcs = df_joined_ecat_bcs[mask].copy()

        # Sort and deduplicate the final dataframe
        df_joined_ecat_bcs = df_joined_ecat_bcs.sort_values(
            by=['land1', 'scope', 'activity_type', 'activity_id', 'bbid', 'filename']
        ).drop_duplicates()

        # Split dataframe into separate ECAT and BCS dataframes
        df_joined_ecat = df_joined_ecat_bcs[df_joined_ecat_bcs['activity_type'] == 'ECAT'].copy()
        df_joined_bcs = df_joined_ecat_bcs[df_joined_ecat_bcs['activity_type'] == 'BCS'].copy()

        # Sort and deduplicate each dataframe
        df_joined_ecat = df_joined_ecat.sort_values(
            by=['land1', 'scope', 'activity_type', 'activity_id', 'bbid', 'filename']).drop_duplicates()
        df_joined_bcs = df_joined_bcs.sort_values(
            by=['land1', 'scope', 'activity_type', 'activity_id', 'bbid', 'filename']).drop_duplicates()

        # Step 6a: Inner join with building block image sub-object data
        df_bb_img_sobj = self.get_df_bb_img_sobj()
        df_joined_img = pd.merge(df_joined_img, df_bb_img_sobj, left_on='fieldname', right_on='task_id', how='left',
                                 suffixes=('', '_on_sobj_level'))  # enhance
        # Remove records where filename_on_sobj_level is empty or contains only whitespace
        df_joined_img = df_joined_img[
            df_joined_img['filename_on_sobj_level'].notna() &
            (df_joined_img['filename_on_sobj_level'].str.strip() != '')
            ]

        # Sort and deduplicate the final dataframe
        df_joined_img = df_joined_img.sort_values(
            by=['land1', 'scope', 'activity_type', 'activity_id', 'bbid', 'filename']).drop_duplicates()

        return df_joined_img, df_joined_ecat, df_joined_bcs

    # ========== High-level Data Combination Methods ==========

    def get_sobj_definition(self):
        """
        Get and merge sub-object definition information

        Returns:
            pd.DataFrame: Merged sub-object definition data
        """
        df_sobj_def = self.get_df_sobj_def()
        df_sobj_def_i = self.get_df_sobj_def_i()
        return pd.merge(df_sobj_def, df_sobj_def_i, on='guid', how='inner')

    def get_bc_field_definition(self):
        """
        Merge BC definition with field information

        Returns:
            pd.DataFrame: Merged BC definition and field data
        """
        df_bc_def_info = self.get_df_bc_def_info()
        df_bc_variant = self.get_df_bc_variant()
        df_field_info = self.get_df_field_info()

        # First merge BC definition with variant
        df_bc_def_variant = pd.merge(
            df_bc_def_info,
            df_bc_variant,
            on='bcset_id',
            how='inner'
        )

        # Second merge with field information
        return pd.merge(
            df_bc_def_variant,
            df_field_info,
            on=['tablename', 'fieldname'],
            how='inner'
        )

    def get_ecat_fld_definition(self):
        """
        Merge ECAT definition with SB_T_005 table

        Returns:
            pd.DataFrame: Merged ECAT definition data
        """
        df_ecat_def = self.get_df_ecat_def()
        df_sb_t_005 = self.get_df_sb_t_005()
        return pd.merge(
            df_ecat_def,
            df_sb_t_005,
            on='objid',
            how='inner'
        )


if __name__ == "__main__":
    """Main execution block for testing purposes"""
    sol_build_r8k = SolBuild('R8K.079')
    dev_test = sol_build_r8k.get_df_bb_to_scopeitem(land1='US')
    print(dev_test.head())
